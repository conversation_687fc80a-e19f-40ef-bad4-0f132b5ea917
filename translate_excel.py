import pandas as pd
from googletrans import Translator
import time
import re

def translate_text_safe(translator, text, max_retries=3):
    """Safely translate text with retry mechanism"""
    if pd.isna(text) or text == '':
        return text

    text_str = str(text)
    # Check if text contains Chinese characters
    if not any('\u4e00' <= char <= '\u9fff' for char in text_str):
        return text_str

    for attempt in range(max_retries):
        try:
            # Add delay to avoid rate limiting
            time.sleep(0.1)
            result = translator.translate(text_str, src='zh-cn', dest='en')
            return result.text
        except Exception as e:
            print(f"Translation attempt {attempt + 1} failed for '{text_str[:50]}...': {e}")
            if attempt < max_retries - 1:
                time.sleep(1)  # Wait longer before retry
            else:
                print(f"Failed to translate: {text_str}")
                return text_str  # Return original text if all attempts fail

    return text_str

def translate_excel(input_file, output_file):
    # Initialize translator
    translator = Translator()

    # Read all sheets from the input Excel file
    try:
        xls = pd.ExcelFile(input_file)
        sheets = xls.sheet_names
        print(f"Found sheets: {sheets}")
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return

    # Create a Pandas Excel writer for the output file
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        for sheet_name in sheets:
            print(f"Processing sheet: {sheet_name}")
            try:
                # Read the sheet
                df = pd.read_excel(input_file, sheet_name=sheet_name)
                print(f"Sheet {sheet_name} has {len(df)} rows and {len(df.columns)} columns")

                # Iterate through each cell and translate Chinese to English
                for col in df.columns:
                    if df[col].dtype == 'object':  # Check if the column contains text
                        print(f"Translating column: {col}")
                        df[col] = df[col].apply(lambda x: translate_text_safe(translator, x))

                # Write the translated DataFrame to the output file
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                print(f"Completed sheet: {sheet_name}")

            except Exception as e:
                print(f"Error processing sheet {sheet_name}: {e}")
                # Still try to save an empty sheet to avoid the "no visible sheets" error
                empty_df = pd.DataFrame()
                empty_df.to_excel(writer, sheet_name=sheet_name, index=False)

    print(f"Translation completed. Output saved to {output_file}")

if __name__ == "__main__":
    input_file = "/Users/<USER>/Documents/Programs/BYD/RFQ/feature/featurelist_250718.xlsx"
    output_file = "/Users/<USER>/Documents/Programs/BYD/RFQ/feature/featurelist_250718_en.xlsx"
    translate_excel(input_file, output_file)