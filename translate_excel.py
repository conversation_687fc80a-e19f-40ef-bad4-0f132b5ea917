import pandas as pd
import re

def translate_chinese_to_english(text):
    """Direct Chinese to English translation dictionary and rules"""
    if pd.isna(text) or text == '':
        return text

    text_str = str(text).strip()

    # Check if text contains Chinese characters
    if not any('\u4e00' <= char <= '\u9fff' for char in text_str):
        return text_str

    # Translation dictionary for common terms
    translation_dict = {
        # Column headers
        '模块': 'Module',
        '分组': 'Category',
        '功能': 'Function',
        '说明': 'Description',
        '二级功能': 'Secondary Function',
        '三级功能': 'Tertiary Function',
        '子功能': 'Sub Function',
        'BYD说明': 'BYD Description',
        '子功能说明': 'Sub Function Description',
        '交互说明': 'Interaction Description',
        '示例': 'Example',
        '备注': 'Remarks',

        # Sheet names
        'navi feature': 'Navigation Features',
        'navi voice control': 'Navigation Voice Control',

        # 模块 (Module)
        '启动': 'Startup',
        '定位': 'Positioning',
        '地图描画': 'Map Rendering',
        '地图数据': 'Map Data',
        '路径规划': 'Route Planning',
        '导航': 'Navigation',
        '语音': 'Voice',
        '搜索': 'Search',
        '收藏': 'Favorites',
        '设置': 'Settings',
        '娱乐': 'Entertainment',
        '通讯': 'Communication',
        '车辆': 'Vehicle',
        '系统': 'System',
        '交通信息': 'Traffic Information',
        '在线服务': 'Online Services',

        # 分组 (Group)
        '定位系统': 'Positioning System',
        '地图视图': 'Map View',
        '地图操作': 'Map Operation',
        '地图显示': 'Map Display',
        '路径计算': 'Route Calculation',
        '导航引导': 'Navigation Guidance',
        '语音控制': 'Voice Control',
        '语音播报': 'Voice Announcement',
        '兴趣点搜索': 'POI Search',
        '地址搜索': 'Address Search',
        '收藏管理': 'Favorites Management',
        '系统设置': 'System Settings',
        '显示设置': 'Display Settings',
        '交通信息': 'Traffic Information',
        '在线更新': 'Online Update',

        # 功能 (Function)
        '启动页面显示': 'Startup Page Display',
        '卫星定位': 'Satellite Positioning',
        'DR': 'Dead Reckoning',
        '2D 北向上': '2D North Up',
        '2D 车头向上': '2D Heading Up',
        '3D视图': '3D View',
        '地图缩放': 'Map Zoom',
        '地图拖拽': 'Map Drag',
        '地图旋转': 'Map Rotation',
        '兴趣点显示': 'POI Display',
        '路径规划': 'Route Planning',
        '语音识别': 'Voice Recognition',
        '语音合成': 'Voice Synthesis',
        '搜索功能': 'Search Function',
        '收藏点': 'Favorite Points',
        '历史记录': 'History Records',
        '实时交通': 'Real-time Traffic',
        '路况信息': 'Traffic Conditions',
        '电子眼': 'Speed Camera',
        '限速提醒': 'Speed Limit Alert',
        '导航语音': 'Navigation Voice',
        '路径重算': 'Route Recalculation',
        '偏航提醒': 'Off-route Alert',
        '到达提醒': 'Arrival Alert',
        '全球视图': 'Global View',
        '比例尺': 'Scale',
        '地图要素': 'Map Elements',
        '标记点显示': 'Marker Display',
        '地图底图': 'Base Map',
        '3D地图': '3D Map',
        '3D Map': '3D Map',
        '地图旋转': 'Map Rotation',
        '地图倾斜': 'Map Tilt',
        '建筑物3D': '3D Buildings',
        '地标显示': 'Landmark Display',
        '道路显示': 'Road Display',
        '兴趣点': 'POI',
        '兴趣点显示': 'POI Display',
        '地图拖动': 'Map Drag',
        '手势操作': 'Gesture Operation',
        '双击': 'Double Click',
        '单击': 'Single Click',
        '长按': 'Long Press',
        '滑动': 'Swipe',
        '捏合': 'Pinch',
        '放大': 'Zoom In',
        '缩小': 'Zoom Out',
        '平移': 'Pan',
        '旋转': 'Rotate',
        '倾斜': 'Tilt',
        '复位': 'Reset',
        '自动': 'Auto',
        '手动': 'Manual',
        '开启': 'Enable',
        '关闭': 'Disable',
        '打开': 'Open',
        '关闭': 'Close',
        '选择': 'Select',
        '确认': 'Confirm',
        '取消': 'Cancel',
        '返回': 'Return',
        '退出': 'Exit',
        '进入': 'Enter',
        '切换': 'Switch',
        '模式': 'Mode',
        '状态': 'Status',
        '界面': 'Interface',
        '菜单': 'Menu',
        '按钮': 'Button',
        '列表': 'List',
        '详情': 'Details',
        '信息': 'Information',
        '数据': 'Data',
        '文件': 'File',
        '格式': 'Format',
        '版本': 'Version',
        '更新': 'Update',
        '下载': 'Download',
        '上传': 'Upload',
        '同步': 'Sync',
        '备份': 'Backup',
        '恢复': 'Restore',
        '导入': 'Import',
        '导出': 'Export',
        '保存': 'Save',
        '删除': 'Delete',
        '编辑': 'Edit',
        '添加': 'Add',
        '修改': 'Modify',
        '查看': 'View',
        '浏览': 'Browse',
        '搜索': 'Search',
        '查找': 'Find',
        '筛选': 'Filter',
        '排序': 'Sort',
        '分类': 'Category',
        '标签': 'Tag',
        '标记': 'Mark',
        '收藏': 'Favorite',
        '分享': 'Share',
        '发送': 'Send',
        '接收': 'Receive',
        '连接': 'Connect',
        '断开': 'Disconnect',
        '网络': 'Network',
        '在线': 'Online',
        '离线': 'Offline',
        '本地': 'Local',
        '远程': 'Remote',
        '云端': 'Cloud',
        '服务器': 'Server',
        '客户端': 'Client',
        '用户': 'User',
        '账户': 'Account',
        '登录': 'Login',
        '注销': 'Logout',
        '注册': 'Register',
        '权限': 'Permission',
        '安全': 'Security',
        '隐私': 'Privacy',
        '设置': 'Settings',
        '配置': 'Configuration',
        '参数': 'Parameter',
        '选项': 'Option',
        '属性': 'Property',
        '特性': 'Feature',
        '功能': 'Function',
        '操作': 'Operation',
        '动作': 'Action',
        '事件': 'Event',
        '响应': 'Response',
        '反馈': 'Feedback',
        '提示': 'Prompt',
        '警告': 'Warning',
        '错误': 'Error',
        '异常': 'Exception',
        '故障': 'Fault',
        '修复': 'Fix',
        '解决': 'Solve',
        '处理': 'Handle',
        '执行': 'Execute',
        '运行': 'Run',
        '启动': 'Start',
        '停止': 'Stop',
        '暂停': 'Pause',
        '继续': 'Continue',
        '重启': 'Restart',
        '刷新': 'Refresh',
        '加载': 'Load',
        '卸载': 'Unload',
        '安装': 'Install',
        '卸载': 'Uninstall',
        '升级': 'Upgrade',
        '降级': 'Downgrade',
        '检查': 'Check',
        '验证': 'Verify',
        '测试': 'Test',
        '调试': 'Debug',
        '监控': 'Monitor',
        '统计': 'Statistics',
        '分析': 'Analysis',
        '报告': 'Report',
        '日志': 'Log',
        '记录': 'Record',
        '历史': 'History',
        '缓存': 'Cache',
        '临时': 'Temporary',
        '永久': 'Permanent',
        '默认': 'Default',
        '自定义': 'Custom',
        '标准': 'Standard',
        '高级': 'Advanced',
        '基本': 'Basic',
        '简单': 'Simple',
        '复杂': 'Complex',
        '快速': 'Fast',
        '慢速': 'Slow',
        '正常': 'Normal',
        '异常': 'Abnormal',
        '有效': 'Valid',
        '无效': 'Invalid',
        '可用': 'Available',
        '不可用': 'Unavailable',
        '启用': 'Enabled',
        '禁用': 'Disabled',
        '激活': 'Active',
        '非激活': 'Inactive',
        '显示': 'Show',
        '隐藏': 'Hide',
        '可见': 'Visible',
        '不可见': 'Invisible',
        '透明': 'Transparent',
        '不透明': 'Opaque',
        '颜色': 'Color',
        '大小': 'Size',
        '位置': 'Position',
        '方向': 'Direction',
        '角度': 'Angle',
        '距离': 'Distance',
        '速度': 'Speed',
        '时间': 'Time',
        '日期': 'Date',
        '年': 'Year',
        '月': 'Month',
        '日': 'Day',
        '小时': 'Hour',
        '分钟': 'Minute',
        '秒': 'Second',
        '毫秒': 'Millisecond',
        '单位': 'Unit',
        '数量': 'Quantity',
        '总数': 'Total',
        '计数': 'Count',
        '序号': 'Number',
        '编号': 'ID',
        '名称': 'Name',
        '标题': 'Title',
        '描述': 'Description',
        '说明': 'Description',
        '备注': 'Remarks',
        '注释': 'Comment',
        '标签': 'Label',
        '类型': 'Type',
        '种类': 'Kind',
        '分组': 'Group',
        '分类': 'Category',
        '级别': 'Level',
        '等级': 'Grade',
        '优先级': 'Priority',
        '重要性': 'Importance',
        '紧急': 'Urgent',
        '普通': 'Normal',
        '低': 'Low',
        '中': 'Medium',
        '高': 'High',
        '最高': 'Highest',
        '最低': 'Lowest',

        # 说明 (Description) - Common phrases
        '支持': 'Support',
        '显示': 'Display',
        '功能': 'Function',
        '系统': 'System',
        '可根据': 'Can be customized according to',
        '车厂需求': 'OEM requirements',
        '定制': 'Customization',
        '进度条': 'Progress bar',
        '融合结果': 'Fusion result',
        '惯导': 'Inertial navigation',
        '始终以': 'Always in',
        '地图': 'Map',
        '车头': 'Vehicle heading',
        '方向': 'Direction',
        '北向上显示': 'North-up display',
        '车头向上显示': 'Heading-up display',
        'GPS系统定位': 'GPS system positioning',
        '系统惯导融合结果定位': 'System inertial navigation fusion positioning',
        '可根据车厂需求定制': 'Can be customized according to OEM requirements',
        '进度条显示，启动页面可根据车厂需求定制': 'Progress bar display, startup page can be customized according to OEM requirements',
        '地图始终以北向上显示': 'Map always displays with north up',
        '地图始终以车头向上显示': 'Map always displays with heading up',
        '最小比例尺时显示全球视图': 'Display global view at minimum scale',
        '支持所有比例尺范围的连续缩放': 'Support continuous zoom for all scale ranges',
        '支持显示不同的图标，标志和信号': 'Support display of different icons, signs and signals',
        '起点': 'Start point',
        '终点': 'End point',
        '中途点': 'Waypoint',
        '家': 'Home',
        '公司': 'Company',
        '收藏': 'Favorites',
        '面状物': 'Area features',
        '绿地': 'Green areas',
        '水系': 'Water systems',
        '背景线': 'Background lines',
        '铁路': 'Railway',
        '道路': 'roads',
        '等': 'etc.',
        '点状物': 'point features',
        '图标': 'icons',
        '文字': 'text',
        '道路名': 'road names',
        '地名': 'place names',
        '地图附件': 'map accessories',
        '目的地图标': 'destination icon',
        '自车图标': 'vehicle icon',
        '指北针': 'compass',
        '如': 'such as',
        '点': 'point',
        '标志': 'signs',
        '信号': 'signals',
        '图标显示': 'icon display',
        '支持显示不同的图标，标志和信号。如起点，终点，中途点，家，公司，收藏点等图标显示': 'Support display of different icons, signs and signals, such as start point, end point, waypoint, home, company, favorite points and other icon displays',
        '1.   面状物:  绿地、水系等\n2.   背景线:  铁路、道路等\n3.   点状物：POI 图标、文字（道路名、地名）\n4.   地图附件：目的地图标、  自车图标、指北针、比例尺': '1. Area features: green areas, water systems, etc.\n2. Background lines: railway, roads, etc.\n3. Point features: POI icons, text (road names, place names)\n4. Map accessories: destination icon, vehicle icon, compass, scale',
        '道路描画': 'Road Rendering',
        '标志性建筑物': 'landmark buildings',
        '3D模型': '3D model',
        '对于某些标志性建筑物，显示该建筑的3D模型': 'Display 3D models for certain landmark buildings',
        '宽度': 'width',
        '颜色': 'color',
        '绘制顺序': 'rendering order',
        '显示': 'display',
        '描画': 'rendering',
        '同等级': 'same level',
        '描绘': 'draw',
        '一致': 'consistent',
        '比例尺': 'scale',
        '设定': 'set',
        '不同': 'different',
        '等级': 'level',
        '设置': 'set',
        '按实际': 'according to actual',
        '压盖': 'overlay',
        '绘制': 'render',
        '体现': 'reflect',
        '层级': 'hierarchy',
        '根据': 'according to',
        '属性': 'attributes',
        '区分': 'distinguish',
        '线路': 'route',
        '连贯': 'continuous',
        '不能': 'cannot',
        '出现': 'appear',
        '断线': 'broken lines',
        '断路': 'broken roads',
        '情况': 'situation',
        '交汇处': 'intersection',
        '线条': 'lines',
        '处理': 'processing',
        '流畅': 'smooth',
        '交汇': 'intersection',
        '过渡': 'transition',
    }

    # Try direct dictionary lookup first
    if text_str in translation_dict:
        return translation_dict[text_str]

    # For longer phrases, try to translate parts
    result = text_str

    # Sort by length (longest first) to avoid partial replacements
    sorted_dict = sorted(translation_dict.items(), key=lambda x: len(x[0]), reverse=True)
    for chinese, english in sorted_dict:
        result = result.replace(chinese, english)

    # If still contains Chinese, apply pattern-based translation
    if any('\u4e00' <= char <= '\u9fff' for char in result):
        # Common patterns
        result = re.sub(r'支持\s*(.+)', r'Support \1', result)
        result = re.sub(r'(.+)显示', r'\1 display', result)
        result = re.sub(r'(.+)功能', r'\1 function', result)
        result = re.sub(r'(.+)系统', r'\1 system', result)
        result = re.sub(r'(.+)设置', r'\1 settings', result)
        result = re.sub(r'(.+)管理', r'\1 management', result)
        result = re.sub(r'(.+)控制', r'\1 control', result)
        result = re.sub(r'(.+)信息', r'\1 information', result)
        result = re.sub(r'(.+)服务', r'\1 service', result)
        result = re.sub(r'(.+)更新', r'\1 update', result)
        result = re.sub(r'(.+)提醒', r'\1 alert', result)
        result = re.sub(r'(.+)播报', r'\1 announcement', result)
        result = re.sub(r'(.+)识别', r'\1 recognition', result)
        result = re.sub(r'(.+)合成', r'\1 synthesis', result)
        result = re.sub(r'(.+)计算', r'\1 calculation', result)
        result = re.sub(r'(.+)引导', r'\1 guidance', result)
        result = re.sub(r'(.+)搜索', r'\1 search', result)
        result = re.sub(r'(.+)记录', r'\1 records', result)
        result = re.sub(r'(.+)视图', r'\1 view', result)
        result = re.sub(r'(.+)操作', r'\1 operation', result)
        result = re.sub(r'(.+)缩放', r'\1 zoom', result)
        result = re.sub(r'(.+)拖拽', r'\1 drag', result)
        result = re.sub(r'(.+)旋转', r'\1 rotation', result)

        # Clean up common Chinese punctuation and connectors
        result = result.replace('，', ', ')
        result = result.replace('。', '. ')
        result = result.replace('；', '; ')
        result = result.replace('：', ': ')
        result = result.replace('（', ' (')
        result = result.replace('）', ') ')
        result = result.replace('、', ', ')
        result = result.replace('和', ' and ')
        result = result.replace('或', ' or ')
        result = result.replace('的', ' ')
        result = result.replace('等', ' etc.')

        # If still has Chinese, mark it for manual review
        if any('\u4e00' <= char <= '\u9fff' for char in result):
            return f"[TRANSLATE: {text_str}]"

    # Clean up extra spaces
    result = re.sub(r'\s+', ' ', result).strip()

    return result

def translate_excel(input_file, output_file):
    # Read all sheets from the input Excel file
    try:
        xls = pd.ExcelFile(input_file)
        sheets = xls.sheet_names
        print(f"Found sheets: {sheets}")
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return

    # Create a Pandas Excel writer for the output file
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        for sheet_name in sheets:
            print(f"Processing sheet: {sheet_name}")
            try:
                # Read the sheet
                df = pd.read_excel(input_file, sheet_name=sheet_name)
                print(f"Sheet {sheet_name} has {len(df)} rows and {len(df.columns)} columns")

                # Translate column headers first
                new_columns = []
                for col in df.columns:
                    translated_col = translate_chinese_to_english(col)
                    new_columns.append(translated_col)
                    print(f"Column '{col}' -> '{translated_col}'")
                df.columns = new_columns

                # Iterate through each cell and translate Chinese to English
                for col in df.columns:
                    if df[col].dtype == 'object':  # Check if the column contains text
                        print(f"Translating content in column: {col}")
                        df[col] = df[col].apply(translate_chinese_to_english)

                # Translate sheet name
                translated_sheet_name = translate_chinese_to_english(sheet_name)
                if translated_sheet_name.startswith('[TRANSLATE:'):
                    # If translation failed, use original name
                    translated_sheet_name = sheet_name

                # Write the translated DataFrame to the output file
                df.to_excel(writer, sheet_name=translated_sheet_name, index=False)
                print(f"Completed sheet: {sheet_name} -> {translated_sheet_name}")

            except Exception as e:
                print(f"Error processing sheet {sheet_name}: {e}")
                # Still try to save an empty sheet to avoid the "no visible sheets" error
                empty_df = pd.DataFrame()
                empty_df.to_excel(writer, sheet_name=sheet_name, index=False)

    print(f"Translation completed. Output saved to {output_file}")

if __name__ == "__main__":
    input_file = "/Users/<USER>/Documents/Programs/BYD/RFQ/feature/featurelist_250718.xlsx"
    output_file = "/Users/<USER>/Documents/Programs/BYD/RFQ/feature/featurelist_250718_en.xlsx"
    translate_excel(input_file, output_file)