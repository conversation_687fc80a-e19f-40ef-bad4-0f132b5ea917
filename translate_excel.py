import pandas as pd
from googletrans import Translator

def translate_excel(input_file, output_file):
    # Initialize translator
    translator = Translator()
    
    # Read all sheets from the input Excel file
    xls = pd.ExcelFile(input_file)
    sheets = xls.sheet_names
    
    # Create a Pandas Excel writer for the output file
    with pd.ExcelWriter(output_file) as writer:
        for sheet in sheets:
            # Read the sheet
            df = pd.read_excel(input_file, sheet_name=sheet)
            
            # Iterate through each cell and translate Chinese to English
            for col in df.columns:
                if df[col].dtype == 'object':  # Check if the column contains text
                    df[col] = df[col].apply(lambda x: translator.translate(str(x), src='zh-cn', dest='en').text if pd.notna(x) and any('\u4e00' <= char <= '\u9fff' for char in str(x)) else x)
            
            # Write the translated DataFrame to the output file
            df.to_excel(writer, sheet_name=sheet, index=False)
    
    print(f"Translation completed. Output saved to {output_file}")

if __name__ == "__main__":
    input_file = "/Users/<USER>/Documents/Programs/BYD/RFQ/feature/featurelist_250718.xlsx"
    output_file = "/Users/<USER>/Documents/Programs/BYD/RFQ/feature/featurelist_250718_en.xlsx"
    translate_excel(input_file, output_file)